import { useEffect, useState } from 'react'
import dayjs from 'dayjs'

const Countdown = ({ endTime }: { endTime: number | string }) => {
  const [timeStr, setTimeStr] = useState('00:00:00')

  useEffect(() => {
    const update = () => {
      const now = dayjs()
      const end = dayjs(endTime)
      let diff = end.diff(now, 'second')
      if (diff < 0) diff = 0
      const h = String(Math.floor(diff / 3600)).padStart(2, '0')
      const m = String(Math.floor((diff % 3600) / 60)).padStart(2, '0')
      const s = String(diff % 60).padStart(2, '0')
      setTimeStr(`${h}:${m}:${s}`)
    }
    update()
    const timer = setInterval(update, 1000)
    return () => clearInterval(timer)
  }, [endTime])

  return <div className="text-[#E40032]">{timeStr}</div>
}

export default Countdown
