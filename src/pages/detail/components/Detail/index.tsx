import { ReadyTemplateSaveRes } from '@/api/detail'
import { Image } from '@tarojs/components'

const Detail = ({ data }: { data: ReadyTemplateSaveRes['data'] }) => {
  const { content } = data
  const imgs = content.split(';')
  return (
    <div className="">
      <div className="bg-white">
        {/* <div className="flex items-center mb-[26px]">
          <div className="w-[4px] h-[26px] bg-[#D8D8D8] mr-[10px]"></div>
          <div className="font-medium text-[28px] text-[#202020] leading-[40px] text-left not-italic">详情</div>
        </div> */}
        <div className="text-[26px]">
          {imgs.map((item, index) => (
            <Image key={index} className="w-full" mode="widthFix" src={item} />
          ))}
        </div>
      </div>
    </div>
  )
}

export default Detail
