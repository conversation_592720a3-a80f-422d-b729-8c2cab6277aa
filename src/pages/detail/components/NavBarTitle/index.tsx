import useObj<PERSON>tom from '@/hooks/useObjAtom'
import Taro, { useShareAppMessage, useRouter } from '@tarojs/taro'
import { useEffect, useState } from 'react'

import shareImg from '@/assets/images/detail/share.png'
import retImg from '@/assets/images/detail/ret.png'
import { activeTabState } from '@/store/inspiration'

const NavBarTitle = ({ id }: { id: string }) => {
  const router = useRouter()
  const { channel = '' } = router.params
  const [height, setHeight] = useState(0)
  const activeTab = useObjAtom(activeTabState)

  useEffect(() => {
    if (Taro.getEnv() === 'WEAPP') {
      const rect = Taro.getMenuButtonBoundingClientRect()
      const { top } = rect || {}
      setHeight(top + 42)
    } else {
      setHeight(100)
    }
  }, [])

  const ret = () => {
    if (channel === 'share') {
      Taro.reLaunch({
        url: '/pages/index/index'
      })
      return
    }
    Taro.navigateBack({ delta: 1 }).catch(() => {
      Taro.reLaunch({
        url: '/pages/index/index'
      })
    })
  }

  const share = () => {
    // Taro.showShareMenu({
    //   withShareTicket: true,
    //   menus: ['shareAppMessage', 'shareTimeline']
    // })
  }

  // 分享
  useShareAppMessage(() => {
    return {
      title: '爱定制', // 分享卡片的title
      path: `/pages/detail/index?id=${id}&channel=share` // 分享卡片的小程序路径
    }
  })

  return (
    <>
      <div className="flex items-end w-full fixed top-0 z-50" style={{ height: `${height}px` }}>
        <div className="flex items-center h-[108px] w-full px-[32px]">
          <div className="w-[166px] flex_center h-[60px] rounded-[30px] border-2 border-solid border-[#EFEFEE] bg-white flex_center">
            <img onClick={ret} className="w-[48px] h-[48px]" src={retImg} alt="" />
            <div className="w-[2px] h-[32px] opacity-10 border-2 bg-black mx-[12px]"></div>
            <button
              open-type="share"
              className="leading-[inherit] m-0 p-0 rounded-none border-none border-[none] bg-transparent hide-after h-full flex_center"
            >
              <img onClick={share} className="w-[48px] h-[48px]" src={shareImg} alt="" />
            </button>
          </div>
          <div className="flex-1 flex_center gap-[52px]"></div>
          <div className="w-[166px]"></div>
        </div>
      </div>
      {/* <div className="bg-white flex items-end w-full" style={{ height: `${height}px` }}></div> */}
    </>
  )
}

export default NavBarTitle
